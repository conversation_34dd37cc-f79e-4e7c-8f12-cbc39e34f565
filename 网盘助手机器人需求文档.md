# 网盘助手机器人需求文档

## 1. 产品概述
网盘助手是一个基于用户Telegram账号的自动化工具，旨在简化用户将他人分享的网盘资源转存到自己账户指定目录的过程。该系统通过 `telethon` 直接监听已加入的频道，自动完成资源转存操作，并支持多目录管理。

## 2. 目标用户
- 经常需要转存他人分享的网盘资源的用户
- 希望将不同资源分类存储到不同目录的用户
- 需要自动化处理大量转存操作的用户

## 3. 功能需求

### 3.1 核心功能
#### 3.1.1 用户账号监听
- 使用用户Telegram账号的 `api_id` 和 `api_hash` 直接监听频道
- 需用户已加入目标频道（无需机器人权限）
- 通过 `telethon` 库实现自动化消息抓取

#### 3.1.2 网盘资源转存
- 自动解析影视频道中的115网盘分享链接
- 提取链接中的关键信息（如提取码）
- 根据整理规则重命名资源（支持TMDB变量）
- 转存到用户预设的网盘目录

#### 3.1.3 多目录管理
- 支持配置多个转存目标目录
- 每个目录有唯一的CID标识
- 用户可选择资源转存的目标目录

#### 3.1.4 影视频道监听
- 用户通过 `telethon` 直接监听已加入的频道
- 自动提取频道消息中的115网盘链接
- 支持多频道并行监听（需配置频道用户名或ID）
- 自动过滤无效链接和非网盘内容

#### 3.1.5 自定义整理规则
- 支持用户定义文件名和目录结构模板
- 支持动态变量替换（如{date}, {channel_name}等）
- 支持正则表达式匹配和替换
- 提供默认模板和自定义模板选项
- 支持从TMDB获取影片元数据（如名称、年代、导演等）
- 支持TMDB变量（如{tmdb_title}, {tmdb_year}）

### 3.2 配置管理
#### 3.2.1 网盘账户配置
- 支持配置网盘Cookie信息
- 支持多账户管理（可选）

#### 3.2.2 目录CID配置
- 支持添加多个目录CID
- 格式：'文件夹名:cid'
- 支持批量添加（用分号分隔）

#### 3.2.3 通知设置
- 成功/失败通知配置
- 详细日志选项

#### 3.2.4 频道订阅配置
- 支持添加多个监听频道
- 配置格式：频道ID:别名（用于文件名变量）
- 支持批量添加（用分号分隔）

#### 3.2.5 整理规则配置
- 支持配置文件名模板
- 支持配置目录结构模板
- 支持变量占位符（如{date}, {filename}等）
- 支持正则表达式替换规则
- 支持配置TMDB API Key
- 支持TMDB元数据缓存设置

### 3.3 用户交互
#### 3.3.1 命令支持
- `/start` - 开始使用机器人
- `/help` - 获取帮助信息
- `/config` - 配置机器人
- `/list` - 列出所有配置的目录

#### 3.3.2 资源转存流程
1. 用户向机器人发送网盘分享链接
2. 机器人解析链接并请求提取码（如需）
3. 机器人列出可选的目标目录
4. 用户选择目标目录
5. 机器人执行转存操作并返回结果

## 4. 技术实现

### 4.1 系统架构
- 前端：Telegram Bot API
- 后端：Python/Node.js服务
- 存储：轻量级数据库（如SQLite）或配置文件

### 4.2 关键技术
- Telegram Bot API集成
- 网盘API调用（模拟浏览器操作或官方API）
- 正则表达式解析URL和CID
- 多线程/异步处理
- 频道监听技术
  - 使用Telegram客户端API监听频道
  - 实现消息过滤和链接提取
- 文件名模板引擎
  - 实现变量替换功能
  - 支持条件判断和简单逻辑
- TMDB API集成
  - 实现影片元数据查询功能
  - 支持元数据缓存机制

### 4.3 数据格式
#### 4.3.1 配置格式
```
Cookie: UID=22116836_R1_1*
转存cid: 文件夹1:************;文件夹2:2998005643062
离线cid: 下载文件夹1:cid1;下载文件夹2:cid2
```

#### 4.3.2 消息格式
- 用户发送：纯链接或"链接 提取码"
- 机器人响应：Markdown格式的回复

## 5. 非功能需求

### 5.1 性能需求
- 响应时间：<3秒
- 并发能力：支持多用户同时操作

### 5.2 安全需求
- 敏感信息（如Cookie）加密存储
- 限制机器人访问权限
- 输入验证防止注入攻击

### 5.3 可靠性
- 错误处理和恢复机制
- 操作日志记录
- 失败重试机制

## 6. 扩展功能（可选）

### 6.1 批量操作
- 支持批量发送多个链接
- 批量转存到同一目录

### 6.2 离线下载
- 支持配置离线下载目录
- 支持磁力链接/HTTP链接离线下载

### 6.3 统计功能
- 转存历史记录
- 存储空间统计

## 7. 开发计划
1. 第一阶段（2周）：基础框架搭建，Telegram机器人集成
2. 第二阶段（2周）：网盘API集成，转存功能实现
3. 第三阶段（1周）：多目录管理，用户交互优化
4. 第四阶段（1周）：测试和bug修复
5. 第五阶段（1周）：频道监听功能实现
6. 第六阶段（1周）：自定义整理功能实现

## 8. 注意事项
1. 确保机器人只处理有效的网盘链接
2. 用户发送链接时应避免多余内容
3. 定期检查网盘API的变更
4. 提供清晰的错误提示信息

## 9. 成功指标
1. 转存成功率 >95%
2. 用户配置时间 <5分钟
3. 平均转存时间 <30秒
4. 用户满意度 >90%